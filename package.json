{"name": "emails", "module": "src/index.ts", "type": "module", "private": true, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sdyachok.com.ua"}, "scripts": {"dev": "bun run --watch src/index.ts", "start": "bun run src/index.ts", "lint": "eslint . --ext .ts --fix", "lint:check": "eslint . --ext .ts", "format": "prettier --write .", "format:check": "prettier --check ."}, "devDependencies": {"@eslint/js": "^9.33.0", "@ianvs/prettier-plugin-sort-imports": "^4.6.2", "@types/bun": "latest", "@types/express": "^5.0.3", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "globals": "^16.3.0", "jiti": "^2.5.1", "prettier": "^3.6.2", "typescript-eslint": "^8.40.0"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"dotenv": "^17.2.1", "dotenv-expand": "^12.0.2", "express": "^5.1.0", "pino": "^9.9.0", "pino-http": "^10.5.0", "pino-pretty": "^13.1.1", "zod": "^4.0.17"}}
import express from 'express';
import { pinoHttp } from 'pino-http';

import env from '~/lib/env';
import logger from '~/lib/logger';

import v1Router from './controllers/v1';

const app = express();

app.use(pinoHttp({ logger }));
app.use(express.static('public'));

app.get('/', (_req, res) => {
  res.json({ message: 'OK', success: true, app: 'Finanze.Pro Emails Service' });
});

app.get('/health', (_req, res) => {
  res.json({ message: 'Healthy', success: true });
});

app.use('/emails/v1', v1Router);

app.listen(env.PORT, () => {
  console.log(`Server is running at http://localhost:${env.PORT}`);
  console.log(`Environment: ${env.NODE_ENV}`);
});
